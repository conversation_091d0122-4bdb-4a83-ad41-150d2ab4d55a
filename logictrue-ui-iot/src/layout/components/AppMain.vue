<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    cachedViews() {
      let arr = [];
      this.$store.state.tagsView.cachedViews.forEach((item) => {
        let temp = '';
        if (item.includes('BusTable')) {
          temp = 'BusTable';
        } else if (item.includes('EqMaintain')) {
          temp = 'EqMaintain';
        } else {
          temp = item;
        }

        arr.push(temp);
      });
      // console.log(arr)
      return arr;
    },
    key() {
      return this.$route.path;
    },
  },
};
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  height: 100%;
  width: 100%;
  position: relative;
  overflow: auto;
  padding: 10px 10px 10px 10px;
}


</style>

