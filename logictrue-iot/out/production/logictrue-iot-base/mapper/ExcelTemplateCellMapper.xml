<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.iot.mapper.ExcelTemplateCellMapper">

    <resultMap type="com.logictrue.iot.entity.ExcelTemplateCell" id="ExcelTemplateCellResult">
        <result property="id" column="id"/>
        <result property="templateId" column="template_id"/>
        <result property="sheetId" column="sheet_id"/>
        <result property="sheetName" column="sheet_name"/>
        <result property="sheetIndex" column="sheet_index"/>
        <result property="rowIndex" column="row_index"/>
        <result property="colIndex" column="col_index"/>
        <result property="cellPosition" column="cell_position"/>
        <result property="content" column="content"/>
        <result property="cellType" column="cell_type"/>
        <result property="fieldType" column="field_type"/>
        <result property="fieldCode" column="field_code"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectExcelTemplateCellVo">
        select id, template_id, sheet_id, sheet_name, sheet_index, row_index, col_index, cell_position, content,
               cell_type, field_type, field_code, sort_order, create_time, update_time
        from excel_template_cell
    </sql>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into excel_template_cell(template_id, sheet_id, sheet_name, sheet_index, row_index, col_index, cell_position, content,
                                       cell_type, field_type, field_code, sort_order, create_time, update_time)
        values
        <foreach collection="cells" item="cell" separator=",">
            (#{cell.templateId}, #{cell.sheetId}, #{cell.sheetName}, #{cell.sheetIndex}, #{cell.rowIndex}, #{cell.colIndex}, #{cell.cellPosition}, #{cell.content},
             #{cell.cellType}, #{cell.fieldType}, #{cell.fieldCode}, #{cell.sortOrder}, now(), now())
        </foreach>
    </insert>

</mapper>
