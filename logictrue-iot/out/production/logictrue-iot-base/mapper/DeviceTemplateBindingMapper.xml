<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.iot.mapper.DeviceTemplateBindingMapper">

    <resultMap type="com.logictrue.iot.entity.DeviceTemplateBinding" id="DeviceTemplateBindingResult">
        <result property="id" column="id"/>
        <result property="deviceCode" column="device_code"/>
        <result property="templateId" column="template_id"/>
        <result property="templateName" column="template_name"/>
        <result property="templateCode" column="template_code"/>
        <result property="bindStatus" column="bind_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <resultMap type="com.logictrue.iot.entity.vo.DeviceTemplateBindingVO" id="DeviceTemplateBindingVOResult">
        <result property="id" column="id"/>
        <result property="deviceCode" column="device_code"/>
        <result property="deviceName" column="device_name"/>
        <result property="deviceType" column="device_type"/>
        <result property="templateId" column="template_id"/>
        <result property="templateName" column="template_name"/>
        <result property="templateCode" column="template_code"/>
        <result property="bindStatus" column="bind_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 分页查询设备模板绑定列表（关联设备信息） -->
    <select id="selectBindingPageWithDevice" resultMap="DeviceTemplateBindingVOResult">
        SELECT
            dtb.id,
            dtb.device_code,
            d.device_name,
            d.device_type,
            dtb.template_id,
            dtb.template_name,
            dtb.template_code,
            dtb.bind_status,
            dtb.create_by,
            dtb.create_time,
            dtb.update_by,
            dtb.update_time,
            dtb.remark
        FROM device_template_binding dtb
        LEFT JOIN drl_device d ON dtb.device_code = d.device_code
        <where>
            <if test="query.deviceCode != null and query.deviceCode != ''">
                AND dtb.device_code LIKE CONCAT('%', #{query.deviceCode}, '%')
            </if>
            <if test="query.deviceName != null and query.deviceName != ''">
                AND d.device_name LIKE CONCAT('%', #{query.deviceName}, '%')
            </if>
            <if test="query.deviceType != null and query.deviceType != ''">
                AND d.device_type LIKE CONCAT('%', #{query.deviceType}, '%')
            </if>
            <if test="query.templateId != null">
                AND dtb.template_id = #{query.templateId}
            </if>
            <if test="query.templateName != null and query.templateName != ''">
                AND dtb.template_name LIKE CONCAT('%', #{query.templateName}, '%')
            </if>
            <if test="query.templateCode != null and query.templateCode != ''">
                AND dtb.template_code LIKE CONCAT('%', #{query.templateCode}, '%')
            </if>
            <if test="query.bindStatus != null">
                AND dtb.bind_status = #{query.bindStatus}
            </if>
        </where>
        ORDER BY dtb.create_time DESC
    </select>

    <!-- 获取所有可用的设备编码列表（未绑定的设备） -->
    <select id="selectAvailableDeviceCodes" resultType="java.lang.String">
        SELECT d.device_code
        FROM drl_device d
        WHERE d.device_code NOT IN (
            SELECT dtb.device_code
            FROM device_template_binding dtb
            WHERE dtb.bind_status = 1
        )
        ORDER BY d.device_code
    </select>

</mapper>
