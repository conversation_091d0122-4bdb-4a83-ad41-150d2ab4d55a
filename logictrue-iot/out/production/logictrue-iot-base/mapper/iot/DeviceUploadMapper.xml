<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.iot.mapper.DeviceUploadMapper">
  <resultMap id="BaseResultMap" type="com.logictrue.iot.entity.DeviceUpload">
    <!--@mbg.generated-->
    <!--@Table drl_device_upload-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="file_id" jdbcType="INTEGER" property="fileId" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="upload_time" jdbcType="TIMESTAMP" property="uploadTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, file_id, file_path, ip, upload_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from drl_device_upload
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from drl_device_upload
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insertSelective" parameterType="com.logictrue.iot.entity.DeviceUpload">
    <!--@mbg.generated-->
    insert into drl_device_upload
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fileId != null">
        file_id,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="ip != null">
        ip,
      </if>
      <if test="uploadTime != null">
        upload_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="fileId != null">
        #{fileId,jdbcType=INTEGER},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        #{ip,jdbcType=VARCHAR},
      </if>
      <if test="uploadTime != null">
        #{uploadTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.logictrue.iot.entity.DeviceUpload">
    <!--@mbg.generated-->
    update drl_device_upload
    <set>
      <if test="fileId != null">
        file_id = #{fileId,jdbcType=INTEGER},
      </if>
      <if test="filePath != null">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="ip != null">
        ip = #{ip,jdbcType=VARCHAR},
      </if>
      <if test="uploadTime != null">
        upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>



</mapper>
