<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.iot.mapper.DeviceDetectionDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.logictrue.iot.entity.DeviceDetectionData">
        <id column="id" property="id" />
        <result column="device_code" property="deviceCode" />
        <result column="template_id" property="templateId" />
        <result column="template_name" property="templateName" />
        <result column="file_name" property="fileName" />
        <result column="file_path" property="filePath" />
        <result column="file_size" property="fileSize" />
        <result column="parse_status" property="parseStatus" />
        <result column="parse_message" property="parseMessage" />
        <result column="parse_time" property="parseTime" />
        <result column="total_sheets" property="totalSheets" />
        <result column="parsed_sheets" property="parsedSheets" />
        <result column="basic_fields_count" property="basicFieldsCount" />
        <result column="table_rows_count" property="tableRowsCount" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 检测数据VO映射结果 -->
    <resultMap id="DetectionDataVOResultMap" type="com.logictrue.iot.entity.vo.DeviceDetectionDataVO">
        <id column="id" property="id" />
        <result column="device_code" property="deviceCode" />
        <result column="device_name" property="deviceName" />
        <result column="device_type" property="deviceType" />
        <result column="template_id" property="templateId" />
        <result column="template_name" property="templateName" />
        <result column="template_code" property="templateCode" />
        <result column="file_name" property="fileName" />
        <result column="file_path" property="filePath" />
        <result column="file_size" property="fileSize" />
        <result column="parse_status" property="parseStatus" />
        <result column="parse_message" property="parseMessage" />
        <result column="parse_time" property="parseTime" />
        <result column="total_sheets" property="totalSheets" />
        <result column="parsed_sheets" property="parsedSheets" />
        <result column="basic_fields_count" property="basicFieldsCount" />
        <result column="table_rows_count" property="tableRowsCount" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 分页查询设备检测数据列表 -->
    <select id="selectDetectionDataPage" resultMap="DetectionDataVOResultMap">
        SELECT
            ddd.id,
            ddd.device_code,
            d.device_name,
            d.device_type,
            ddd.template_id,
            ddd.template_name,
            et.template_code,
            ddd.file_name,
            ddd.file_path,
            ddd.file_size,
            ddd.parse_status,
            ddd.parse_message,
            ddd.parse_time,
            ddd.total_sheets,
            ddd.parsed_sheets,
            ddd.basic_fields_count,
            ddd.table_rows_count,
            ddd.create_by,
            ddd.create_time,
            ddd.update_by,
            ddd.update_time,
            ddd.remark
        FROM device_detection_data ddd
        LEFT JOIN drl_device d ON ddd.device_code = d.device_code
        LEFT JOIN excel_template et ON ddd.template_id = et.id
        <where>
            <if test="deviceCode != null and deviceCode != ''">
                AND ddd.device_code LIKE CONCAT('%', #{deviceCode}, '%')
            </if>
            <if test="deviceName != null and deviceName != ''">
                AND d.device_name LIKE CONCAT('%', #{deviceName}, '%')
            </if>
            <if test="templateName != null and templateName != ''">
                AND ddd.template_name LIKE CONCAT('%', #{templateName}, '%')
            </if>
            <if test="parseStatus != null">
                AND ddd.parse_status = #{parseStatus}
            </if>
            <if test="startTime != null and startTime != ''">
                AND ddd.create_time >= #{startTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND ddd.create_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY ddd.create_time DESC
    </select>

    <!-- 根据ID查询检测数据详情 -->
    <select id="selectDetectionDataDetail" resultMap="DetectionDataVOResultMap">
        SELECT
            ddd.id,
            ddd.device_code,
            d.device_name,
            d.device_type,
            ddd.template_id,
            ddd.template_name,
            et.template_code,
            ddd.file_name,
            ddd.file_path,
            ddd.file_size,
            ddd.parse_status,
            ddd.parse_message,
            ddd.parse_time,
            ddd.total_sheets,
            ddd.parsed_sheets,
            ddd.basic_fields_count,
            ddd.table_rows_count,
            ddd.create_by,
            ddd.create_time,
            ddd.update_by,
            ddd.update_time,
            ddd.remark
        FROM device_detection_data ddd
        LEFT JOIN drl_device d ON ddd.device_code = d.device_code
        LEFT JOIN excel_template et ON ddd.template_id = et.id
        WHERE ddd.id = #{id}
    </select>

</mapper>
